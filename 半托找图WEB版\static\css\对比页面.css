/* 图片对比页面专用样式 */

/* SKU信息区域 */
.sku-info-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin-bottom: 25px;
}

.sku-info {
    text-align: center;
    padding: 20px;
}

.sku-info h2 {
    font-size: 1.8rem;
    margin-bottom: 10px;
    border: none;
    color: white;
}

.sku-info p {
    font-size: 1.2rem;
    margin-bottom: 15px;
    opacity: 0.9;
}

.progress-info {
    font-size: 1rem;
    opacity: 0.8;
}

/* 图片对比区域 */
.compare-section {
    padding: 0;
    background: transparent;
    box-shadow: none;
}

.compare-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
    margin-bottom: 25px;
}

.image-panel {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.image-panel:hover {
    border-color: #4299e1;
    transform: translateY(-2px);
}

.api-panel {
    border-left: 4px solid #48bb78;
}

.local-panel {
    border-left: 4px solid #9f7aea;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e2e8f0;
}

.panel-header h3 {
    font-size: 1.3rem;
    color: #2d3748;
    margin: 0;
    font-weight: bold;
}

.panel-header h3 i {
    margin-right: 10px;
    color: #4299e1;
}

.image-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 5px;
}

.image-source {
    font-size: 0.9rem;
    color: #4a5568;
    background: #f7fafc;
    padding: 4px 8px;
    border-radius: 4px;
}

/* 图片容器 */
.image-container {
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f7fafc;
    border-radius: 10px;
    border: 2px dashed #e2e8f0;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
}

.compare-image {
    max-width: 100%;
    max-height: 400px;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.3s ease;
}

.compare-image:hover {
    transform: scale(1.05);
}

.loading-placeholder,
.error-placeholder {
    text-align: center;
    color: #4a5568;
    padding: 40px;
}

.loading-placeholder i,
.error-placeholder i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: #a0aec0;
}

.loading-placeholder i {
    color: #4299e1;
}

.error-placeholder i {
    color: #f56565;
}

/* 本地图片网格 */
.local-images-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;
    padding: 10px;
}

.local-image-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.local-image-item:hover {
    border-color: #9f7aea;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.local-image-item.selected {
    border-color: #48bb78;
    background: #f0fff4;
}

.local-image-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    display: block;
}

.local-image-info {
    padding: 8px;
    font-size: 0.8rem;
    color: #4a5568;
    background: white;
}

.local-image-name {
    font-weight: bold;
    margin-bottom: 4px;
    word-break: break-all;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.local-image-size {
    color: #718096;
}

/* 选择指示器 */
.selection-indicator {
    position: absolute;
    top: 5px;
    right: 5px;
    background: #48bb78;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.local-image-item.selected .selection-indicator {
    opacity: 1;
}

/* 面板操作按钮 */
.panel-actions {
    text-align: center;
}

.panel-actions .btn {
    min-width: 150px;
}

/* 操作按钮区域 */
.actions-section {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.action-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

/* 模态框操作按钮 */
.modal-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
}

/* 完成摘要 */
.complete-summary {
    background: #f7fafc;
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
    text-align: left;
    font-family: monospace;
    font-size: 0.9rem;
    max-height: 200px;
    overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .compare-container {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .panel-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .image-info {
        align-items: flex-start;
    }

    .image-container {
        min-height: 300px;
    }

    .local-images-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 10px;
    }

    .local-image-item img {
        height: 100px;
    }

    .action-buttons {
        flex-direction: column;
        align-items: center;
    }

    .modal-actions {
        flex-direction: column;
    }
}