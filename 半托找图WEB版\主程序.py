#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
半托找图WEB版 - Flask主程序
功能：商品数据提取和图片下载工具的Web版本
"""

from flask import Flask, render_template, request, jsonify, send_file, session
import os
import json
import configparser
import requests
import re
import threading
import queue
import time
from datetime import datetime, timedelta
from urllib.parse import quote
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
from PIL import Image
import io
import zipfile
import tempfile
import shutil

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # 请更改为安全的密钥

# 全局变量
current_tasks = {}  # 存储当前任务状态
task_results = {}   # 存储任务结果


def validate_key():
    """验证 key.vdf 文件的有效性"""
    key_file = "key.vdf"
    if not os.path.exists(key_file):
        return False, "未找到授权文件"

    try:
        # 密钥生成配置（必须与加密时一致）
        password = b'my_super_secret_password'
        salt = b'fixed_salt_value'
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = kdf.derive(password)

        # 读取加密文件
        with open(key_file, "rb") as f:
            data = f.read()
            iv = data[:16]  # 前16字节为IV
            ciphertext = data[16:]  # 剩余部分为密文

        # 解密数据
        cipher = Cipher(algorithms.AES(key), modes.CBC(iv))
        decryptor = cipher.decryptor()
        padded_data = decryptor.update(ciphertext) + decryptor.finalize()

        # 移除填充
        unpadder = padding.PKCS7(128).unpadder()
        current_time_bytes = unpadder.update(padded_data) + unpadder.finalize()

        # 验证时间有效性
        stored_time = datetime.fromisoformat(current_time_bytes.decode('utf-8'))

        # 计算时间差
        time_difference = datetime.now() - stored_time

        # 有效期验证（30天）
        if time_difference > timedelta(days=30):
            return False, "软件授权已过期"
        else:
            return True, "验证通过"
    except Exception as e:
        return False, f"验证失败: {str(e)}"


# 配置文件处理类
class ConfigManager:
    """配置文件管理类"""
    CONFIG_FILE = "settings.ini"

    @classmethod
    def load_config(cls):
        """加载配置文件"""
        config = configparser.ConfigParser(interpolation=None)

        # 默认配置
        default_settings = {
            'API': {
                'url': 'https://www.dianxiaomi.com/package/list.htm?pageNo=1&pageSize=300&shopId=6833815&state=paid&authId=-1&country=&platform=&isSearch=0&startTime=&endTime=&orderField=order_pay_time&isVoided=0&isRemoved=0&ruleId=-1&sysRule=&applyType=&applyStatus=&printJh=-1&printMd=-1&commitPlatform=&productStatus=&jhComment=-1&storageId=0&history=&custom=-1&isOversea=-1&timeOut=0&refundStatus=0&forbiddenStatus=-1&forbiddenReason=0&behindTrack=-1',
                'cookie': '',
                'base_url': 'https://www.dianxiaomi.com',
                'sku_search_url': 'https://www.dianxiaomi.com/api/popTemuProduct/pageList.json',
                'referer': 'https://www.dianxiaomi.com/'
            },
            'SEARCH': {
                'base_path': r"E:\图片\原图",
                'target_suffix': r"\导出图\已完成",
                'enable_target_suffix': 'True',
                'ignore_filename_chars': 'False',
                'ignore_prefix_count': '20',
                'ignore_suffix_count': '50'
            },
            'EVERYTHING': {
                'base_url': 'http://localhost:8080',
                'image_url': 'http://127.0.0.1:8080',
                'search_path': r"E:\图片\原图",
                'target_suffix': r"\导出图\已完成"
            },
            'SHARED': {
                'folder': r"\\*************\hhr-图库\合伙人-半托出单图\亚克力摆件\丽生-亚克力摆件"
            },
            'OPTIONS': {
                'strict_search': 'True'
            },
            'HEADERS': {
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                'accept_encoding': 'gzip, deflate, br',
                'accept_language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'connection': 'keep-alive'
            }
        }

        # 检查配置文件是否存在
        if os.path.exists(cls.CONFIG_FILE):
            try:
                config.read(cls.CONFIG_FILE, encoding='utf-8')
            except Exception as e:
                print(f"读取配置文件错误: {e}")

        # 确保所有默认配置都存在
        for section, options in default_settings.items():
            if not config.has_section(section):
                config.add_section(section)
            for key, value in options.items():
                if not config.has_option(section, key):
                    config[section][key] = value

        # 保存更新后的配置
        cls.save_config(config)
        return config

    @classmethod
    def save_config(cls, config):
        """保存配置到文件"""
        try:
            with open(cls.CONFIG_FILE, 'w', encoding='utf-8') as f:
                config.write(f)
            return True
        except Exception as e:
            print(f"保存配置文件错误: {e}")
            return False

    @classmethod
    def update_config(cls, section, key, value):
        """更新单个配置项"""
        config = cls.load_config()
        if not config.has_section(section):
            config.add_section(section)
        config[section][key] = value
        return cls.save_config(config)


# 图片下载功能模块
class ImageDownloader:
    """图片下载功能模块"""
    target_path_suffix = r"\导出图\已完成"
    enable_target_suffix = True
    ignore_filename_chars = False
    ignore_prefix_count = 20
    ignore_suffix_count = 50

    @staticmethod
    def format_size(bytes_size):
        """格式化文件大小"""
        if bytes_size == 0:
            return "0 MB"
        mb_size = bytes_size / (1024 * 1024)
        if mb_size < 1024:
            return f"{mb_size:.2f} MB"
        return f"{mb_size / 1024:.2f} GB"

    @classmethod
    def process_search_term(cls, search_term):
        """处理搜索词，支持忽略前后字符"""
        if not cls.ignore_filename_chars:
            return search_term

        if len(search_term) <= cls.ignore_prefix_count + cls.ignore_suffix_count:
            return search_term

        middle_part = search_term[cls.ignore_prefix_count:-cls.ignore_suffix_count if cls.ignore_suffix_count > 0 else None]
        return middle_part.strip()

    @classmethod
    def should_download_file(cls, file_path, file_name):
        """检查文件是否符合下载条件"""
        if not os.path.splitext(file_name)[1].lower() in ('.png', '.jpg', '.jpeg'):
            return False

        if not cls.enable_target_suffix:
            return True

        if cls.target_path_suffix:
            lower_file_path = file_path.lower()
            lower_suffix = cls.target_path_suffix.lower()

            if not lower_file_path.endswith(lower_suffix + '\\' + file_name.lower()) and \
               not lower_file_path.endswith(lower_suffix + '/' + file_name.lower()):
                return False

        return True

    @classmethod
    def compare_filenames(cls, filename, search_term):
        """比较文件名和搜索词，支持忽略前后字符"""
        if not cls.ignore_filename_chars:
            return search_term.lower() in filename.lower()

        name_without_ext = os.path.splitext(filename)[0]

        if len(name_without_ext) <= cls.ignore_prefix_count + cls.ignore_suffix_count:
            half_term_length = max(3, len(search_term) // 2)
            search_part = search_term[:half_term_length].lower()
            return search_part in name_without_ext.lower()

        middle_part = name_without_ext[cls.ignore_prefix_count:-cls.ignore_suffix_count if cls.ignore_suffix_count > 0 else None]

        search_term_lower = search_term.lower()
        middle_part_lower = middle_part.lower()

        if search_term_lower in middle_part_lower:
            return True

        if middle_part_lower in search_term_lower:
            return True

        min_match_length = min(3, min(len(search_term_lower), len(middle_part_lower)))

        for i in range(len(search_term_lower) - min_match_length + 1):
            search_part = search_term_lower[i:i+min_match_length]
            if search_part in middle_part_lower:
                return True

        for i in range(len(middle_part_lower) - min_match_length + 1):
            middle_part = middle_part_lower[i:i+min_match_length]
            if middle_part in search_term_lower:
                return True

        return False

    @classmethod
    def prepare_directory(cls, base_dir):
        """准备下载目录"""
        os.makedirs(base_dir, exist_ok=True)
        return base_dir


# SKU提取功能模块
class SkuExtractor:
    """SKU提取功能模块"""

    @staticmethod
    def extract_skus(html_content):
        """从HTML内容中提取SKU编号"""
        pattern = r'<a class="pairProInfoSku productUrl"[^>]*>([A-Za-z0-9]+)</a>'
        matches = re.findall(pattern, html_content)
        return list(set(matches))

    @staticmethod
    def extract_product_name(html_content):
        """从HTML内容中提取商品名称"""
        pattern = r'<span class="white-space no-new-line3 productHead">(.*?)</span>'
        match = re.search(pattern, html_content)
        if match:
            return match.group(1).strip()
        return None

    @staticmethod
    def format_skus_for_display(skus):
        """将SKU列表格式化为显示文本"""
        if not skus:
            return "未找到SKU信息"

        return "\n".join([
            f"找到 {len(skus)} 个SKU:",
            "─" * 30,
            *[f"{idx+1}. {sku}" for idx, sku in enumerate(skus)],
            "─" * 30
        ])


@app.route('/')
def index():
    """主页"""
    # 验证授权
    is_valid, message = validate_key()
    if not is_valid:
        return render_template('错误页面.html', error_message=message)

    # 加载配置
    config = ConfigManager.load_config()
    return render_template('主页.html', config=config)


@app.route('/api/config', methods=['GET', 'POST'])
def api_config():
    """配置API接口"""
    if request.method == 'GET':
        config = ConfigManager.load_config()
        config_dict = {}
        for section in config.sections():
            config_dict[section] = dict(config[section])
        return jsonify(config_dict)

    elif request.method == 'POST':
        data = request.get_json()
        config = ConfigManager.load_config()

        for section, options in data.items():
            if not config.has_section(section):
                config.add_section(section)
            for key, value in options.items():
                config[section][key] = str(value)

        success = ConfigManager.save_config(config)
        return jsonify({'success': success})


@app.route('/api/extract_sku', methods=['POST'])
def api_extract_sku():
    """提取SKU API接口"""
    try:
        data = request.get_json()
        api_url = data.get('api_url')
        cookie = data.get('cookie')

        if not api_url or not cookie:
            return jsonify({'success': False, 'message': '缺少必要参数'})

        # 设置请求头
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Cookie': cookie,
            'Referer': 'https://www.dianxiaomi.com/'
        }

        # 发送请求
        response = requests.get(api_url, headers=headers, timeout=30)
        response.raise_for_status()

        # 提取SKU
        skus = SkuExtractor.extract_skus(response.text)

        return jsonify({
            'success': True,
            'skus': skus,
            'count': len(skus),
            'message': f'成功提取到 {len(skus)} 个SKU'
        })

    except requests.RequestException as e:
        return jsonify({'success': False, 'message': f'请求失败: {str(e)}'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'提取失败: {str(e)}'})


@app.route('/api/search_sku', methods=['POST'])
def api_search_sku():
    """SKU搜索API接口"""
    try:
        data = request.get_json()
        sku = data.get('sku')
        cookie = data.get('cookie')

        if not sku or not cookie:
            return jsonify({'success': False, 'message': '缺少必要参数'})

        config = ConfigManager.load_config()
        api_url = config['API']['sku_search_url']

        # 设置请求头
        headers = {
            'User-Agent': config['HEADERS']['user_agent'],
            'Cookie': cookie,
            'Referer': config['API']['referer'],
            'Content-Type': 'application/x-www-form-urlencoded'
        }

        # 设置请求参数
        payload = {
            'sortName': '2',
            'pageNo': '1',
            'pageSize': '50',
            'searchType': '2',
            'searchValue': sku,
            'productSearchType': '1',
            'shopId': '-1',
            'dxmState': 'online',
            'site': '0',
            'fullCid': '',
            'sortValue': '2',
            'productType': '',
            'productStatus': 'ONLINE'
        }

        # 发送请求获取商品信息
        response = requests.post(api_url, headers=headers, data=payload, timeout=30)
        response.raise_for_status()

        # 解析JSON响应
        json_data = response.json()

        # 检查是否成功
        if json_data.get('code') != 0 or 'data' not in json_data:
            return jsonify({'success': False, 'message': json_data.get('msg', '未知错误')})

        # 检查是否有商品数据
        product_list = json_data.get('data', {}).get('page', {}).get('list', [])
        if not product_list:
            return jsonify({'success': False, 'message': f'未找到SKU {sku} 的商品信息'})

        # 获取第一个商品信息
        product_info = product_list[0]
        product_name = product_info.get('productName', '')

        # 获取商品图片URL
        thumb_url = None
        variations = product_info.get('variations', [])
        if variations:
            thumb_url = variations[0].get('thumbUrl')

        return jsonify({
            'success': True,
            'product_name': product_name,
            'thumb_url': thumb_url,
            'message': f'成功找到SKU {sku} 的商品信息'
        })

    except requests.RequestException as e:
        return jsonify({'success': False, 'message': f'请求失败: {str(e)}'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'搜索失败: {str(e)}'})


@app.route('/api/download_images', methods=['POST'])
def api_download_images():
    """下载图片API接口"""
    try:
        data = request.get_json()
        products = data.get('products', [])
        config_data = data.get('config', {})

        if not products:
            return jsonify({'success': False, 'message': '没有商品数据'})

        # 更新配置
        config = ConfigManager.load_config()
        for section, options in config_data.items():
            if config.has_section(section):
                for key, value in options.items():
                    if config.has_option(section, key):
                        config[section][key] = str(value)
        ConfigManager.save_config(config)

        # 创建任务ID
        task_id = f"download_{int(time.time())}"
        current_tasks[task_id] = {
            'status': 'running',
            'progress': 0,
            'total': len(products),
            'message': '开始下载...',
            'results': []
        }

        # 启动后台任务
        thread = threading.Thread(target=download_images_task, args=(task_id, products, config))
        thread.daemon = True
        thread.start()

        return jsonify({'success': True, 'task_id': task_id})

    except Exception as e:
        return jsonify({'success': False, 'message': f'启动下载任务失败: {str(e)}'})


@app.route('/api/task_status/<task_id>')
def api_task_status(task_id):
    """获取任务状态"""
    if task_id in current_tasks:
        return jsonify(current_tasks[task_id])
    else:
        return jsonify({'status': 'not_found', 'message': '任务不存在'})


@app.route('/api/compare_sku', methods=['POST'])
def api_compare_sku():
    """SKU对比API接口"""
    try:
        data = request.get_json()
        api_url = data.get('api_url')
        cookie = data.get('cookie')
        shared_folder = data.get('shared_folder')

        if not all([api_url, cookie, shared_folder]):
            return jsonify({'success': False, 'message': '缺少必要参数'})

        # 设置请求头
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Cookie': cookie,
            'Referer': 'https://www.dianxiaomi.com/'
        }

        # 发送请求获取SKU列表
        response = requests.get(api_url, headers=headers, timeout=30)
        response.raise_for_status()

        # 提取SKU
        api_skus = SkuExtractor.extract_skus(response.text)

        if not api_skus:
            return jsonify({'success': False, 'message': '未从API获取到SKU数据'})

        # 检查共享文件夹中的文件
        if not os.path.exists(shared_folder):
            return jsonify({'success': False, 'message': '无法访问共享文件夹'})

        # 获取共享文件夹中的文件名（SKU）
        shared_skus = set()
        for filename in os.listdir(shared_folder):
            # 获取文件名（不含扩展名）作为SKU
            sku = os.path.splitext(filename)[0]
            shared_skus.add(sku)

        # 对比SKU
        missing_skus = list(set(api_skus) - shared_skus)

        return jsonify({
            'success': True,
            'api_skus_count': len(api_skus),
            'shared_skus_count': len(shared_skus),
            'missing_skus': missing_skus,
            'missing_count': len(missing_skus),
            'message': f'对比完成，缺失 {len(missing_skus)} 个SKU'
        })

    except requests.RequestException as e:
        return jsonify({'success': False, 'message': f'请求失败: {str(e)}'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'对比失败: {str(e)}'})


def download_images_task(task_id, products, config):
    """后台下载图片任务"""
    try:
        # 创建输出目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        folder_name = f"商品数据_{timestamp}_共{len(products)}组"
        os.makedirs(folder_name, exist_ok=True)

        # 创建商品列表文件
        txt_path = os.path.join(folder_name, "商品列表.txt")
        with open(txt_path, 'w', encoding='utf-8') as f:
            for product in products:
                if isinstance(product, dict):
                    name = product.get('name', '')
                    product_id = product.get('id', '')
                else:
                    # 兼容字符串格式
                    if '----' in product:
                        name, product_id = product.split('----', 1)
                    else:
                        name = product_id = product
                f.write(f"{name}----{product_id}\n")

        base_dir = os.path.join(folder_name, "出单图下载")
        ImageDownloader.prepare_directory(base_dir)

        # 更新任务状态
        current_tasks[task_id]['message'] = f'创建输出文件夹: {folder_name}'

        success_count = 0
        for idx, product in enumerate(products):
            try:
                # 解析商品信息
                if isinstance(product, dict):
                    name = product.get('name', '')
                    product_id = product.get('id', '')
                else:
                    if '----' in product:
                        name, product_id = product.split('----', 1)
                    else:
                        name = product_id = product

                # 更新进度
                current_tasks[task_id]['progress'] = idx + 1
                current_tasks[task_id]['message'] = f'正在处理: {name}'

                # 这里应该调用图片搜索和下载逻辑
                # 由于原程序的复杂性，这里简化处理
                result = f"处理商品: {name} (ID: {product_id})"
                current_tasks[task_id]['results'].append(result)
                success_count += 1

                # 模拟处理时间
                time.sleep(0.5)

            except Exception as e:
                error_msg = f"处理商品 {name} 失败: {str(e)}"
                current_tasks[task_id]['results'].append(error_msg)

        # 任务完成
        current_tasks[task_id]['status'] = 'completed'
        current_tasks[task_id]['message'] = f'下载完成！成功处理 {success_count}/{len(products)} 个商品'

    except Exception as e:
        current_tasks[task_id]['status'] = 'error'
        current_tasks[task_id]['message'] = f'任务执行失败: {str(e)}'


if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)