// 半托找图WEB版 - 主脚本文件

// 全局变量
let currentTaskId = null;
let taskCheckInterval = null;

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    loadConfig();
});

// 初始化事件监听器
function initializeEventListeners() {
    // 配置保存按钮
    document.getElementById('save-config-btn').addEventListener('click', saveConfig);

    // 功能按钮
    document.getElementById('extract-sku-btn').addEventListener('click', extractSku);
    document.getElementById('download-images-btn').addEventListener('click', downloadImages);
    document.getElementById('search-sku-btn').addEventListener('click', searchSku);
    document.getElementById('search-sku-download-btn').addEventListener('click', searchSkuDownload);
    document.getElementById('compare-sku-btn').addEventListener('click', compareSku);
    document.getElementById('download-missing-btn').addEventListener('click', downloadMissingSku);
    document.getElementById('rename-files-btn').addEventListener('click', renameFiles);

    // 控制台清空按钮
    document.getElementById('clear-console-btn').addEventListener('click', clearConsole);

    // 进度模态框关闭按钮
    document.getElementById('close-progress-btn').addEventListener('click', closeProgressModal);
}

// 加载配置
async function loadConfig() {
    try {
        const response = await fetch('/api/config');
        const config = await response.json();

        // 填充配置到表单
        if (config.API) {
            document.getElementById('api-url').value = config.API.url || '';
            document.getElementById('cookie').value = config.API.cookie || '';
        }

        if (config.SEARCH) {
            document.getElementById('search-path').value = config.SEARCH.base_path || '';
            document.getElementById('target-suffix').value = config.SEARCH.target_suffix || '';
            document.getElementById('enable-target-suffix').checked = config.SEARCH.enable_target_suffix === 'True';
            document.getElementById('ignore-filename-chars').checked = config.SEARCH.ignore_filename_chars === 'True';
            document.getElementById('ignore-prefix-count').value = config.SEARCH.ignore_prefix_count || '20';
            document.getElementById('ignore-suffix-count').value = config.SEARCH.ignore_suffix_count || '50';
        }

        if (config.SHARED) {
            document.getElementById('shared-folder').value = config.SHARED.folder || '';
        }

        if (config.OPTIONS) {
            document.getElementById('strict-search').checked = config.OPTIONS.strict_search === 'True';
        }

        logMessage('配置加载完成', 'success');
    } catch (error) {
        logMessage('加载配置失败: ' + error.message, 'error');
    }
}

// 保存配置
async function saveConfig() {
    try {
        const config = {
            API: {
                url: document.getElementById('api-url').value,
                cookie: document.getElementById('cookie').value
            },
            SEARCH: {
                base_path: document.getElementById('search-path').value,
                target_suffix: document.getElementById('target-suffix').value,
                enable_target_suffix: document.getElementById('enable-target-suffix').checked.toString(),
                ignore_filename_chars: document.getElementById('ignore-filename-chars').checked.toString(),
                ignore_prefix_count: document.getElementById('ignore-prefix-count').value,
                ignore_suffix_count: document.getElementById('ignore-suffix-count').value
            },
            SHARED: {
                folder: document.getElementById('shared-folder').value
            },
            OPTIONS: {
                strict_search: document.getElementById('strict-search').checked.toString()
            }
        };

        const response = await fetch('/api/config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(config)
        });

        const result = await response.json();

        if (result.success) {
            logMessage('配置保存成功', 'success');
        } else {
            logMessage('配置保存失败', 'error');
        }
    } catch (error) {
        logMessage('保存配置失败: ' + error.message, 'error');
    }
}

// 提取SKU
async function extractSku() {
    try {
        const apiUrl = document.getElementById('api-url').value;
        const cookie = document.getElementById('cookie').value;

        if (!apiUrl || !cookie) {
            logMessage('请先配置API地址和Cookie', 'warning');
            return;
        }

        logMessage('开始提取SKU...', 'info');

        const response = await fetch('/api/extract_sku', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                api_url: apiUrl,
                cookie: cookie
            })
        });

        const result = await response.json();

        if (result.success) {
            logMessage(`成功提取到 ${result.count} 个SKU`, 'success');

            // 将SKU填入商品数据输入框
            const productData = document.getElementById('product-data');
            productData.value = result.skus.join('\n');

            logMessage('SKU已自动填入商品数据输入区', 'info');
        } else {
            logMessage('提取SKU失败: ' + result.message, 'error');
        }
    } catch (error) {
        logMessage('提取SKU失败: ' + error.message, 'error');
    }
}

// 搜索SKU
async function searchSku() {
    try {
        const sku = document.getElementById('sku-search').value.trim();
        const cookie = document.getElementById('cookie').value;

        if (!sku) {
            logMessage('请输入SKU', 'warning');
            return;
        }

        if (!cookie) {
            logMessage('请先配置Cookie', 'warning');
            return;
        }

        logMessage(`开始搜索SKU: ${sku}`, 'info');

        const response = await fetch('/api/search_sku', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                sku: sku,
                cookie: cookie
            })
        });

        const result = await response.json();

        if (result.success) {
            logMessage(`找到商品: ${result.product_name}`, 'success');

            // 将商品信息添加到输入框
            const productData = document.getElementById('product-data');
            const currentText = productData.value.trim();
            const newLine = `${result.product_name}----${sku}`;

            if (currentText) {
                productData.value = currentText + '\n' + newLine;
            } else {
                productData.value = newLine;
            }

            logMessage('商品信息已添加到数据输入区', 'info');

            // 清空SKU搜索框
            document.getElementById('sku-search').value = '';
        } else {
            logMessage('搜索SKU失败: ' + result.message, 'error');
        }
    } catch (error) {
        logMessage('搜索SKU失败: ' + error.message, 'error');
    }
}

// 下载图片
async function downloadImages() {
    try {
        const productData = document.getElementById('product-data').value.trim();

        if (!productData) {
            logMessage('请输入商品数据', 'warning');
            return;
        }

        // 解析商品数据
        const products = productData.split('\n').map(line => {
            line = line.trim();
            if (line.includes('----')) {
                const [name, id] = line.split('----', 2);
                return { name: name.trim(), id: id.trim() };
            } else {
                return { name: line, id: line };
            }
        }).filter(p => p.name);

        if (products.length === 0) {
            logMessage('没有有效的商品数据', 'warning');
            return;
        }

        // 获取当前配置
        const config = getCurrentConfig();

        logMessage(`开始下载 ${products.length} 个商品的图片...`, 'info');

        const response = await fetch('/api/download_images', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                products: products,
                config: config
            })
        });

        const result = await response.json();

        if (result.success) {
            currentTaskId = result.task_id;
            showProgressModal();
            startTaskMonitoring();
        } else {
            logMessage('启动下载任务失败: ' + result.message, 'error');
        }
    } catch (error) {
        logMessage('下载图片失败: ' + error.message, 'error');
    }
}

// SKU搜索下载
async function searchSkuDownload() {
    logMessage('SKU搜索下载功能开发中...', 'info');
}

// SKU对比
async function compareSku() {
    try {
        const apiUrl = document.getElementById('api-url').value;
        const cookie = document.getElementById('cookie').value;
        const sharedFolder = document.getElementById('shared-folder').value;

        if (!apiUrl || !cookie || !sharedFolder) {
            logMessage('请先配置API地址、Cookie和共享文件夹', 'warning');
            return;
        }

        logMessage('开始SKU对比...', 'info');

        const response = await fetch('/api/compare_sku', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                api_url: apiUrl,
                cookie: cookie,
                shared_folder: sharedFolder
            })
        });

        const result = await response.json();

        if (result.success) {
            logMessage(`对比完成！`, 'success');
            logMessage(`API中的SKU总数: ${result.api_skus_count}`, 'info');
            logMessage(`共享文件夹中的SKU总数: ${result.shared_skus_count}`, 'info');
            logMessage(`缺失的SKU数量: ${result.missing_count}`, 'warning');

            if (result.missing_skus.length > 0) {
                // 将缺失的SKU填入输入框
                const productData = document.getElementById('product-data');
                productData.value = result.missing_skus.join('\n');
                logMessage('缺失的SKU已填入商品数据输入区', 'info');
            }
        } else {
            logMessage('SKU对比失败: ' + result.message, 'error');
        }
    } catch (error) {
        logMessage('SKU对比失败: ' + error.message, 'error');
    }
}

// 下载缺失SKU
async function downloadMissingSku() {
    logMessage('下载缺失SKU功能开发中...', 'info');
}

// 重命名文件
async function renameFiles() {
    logMessage('重命名文件功能开发中...', 'info');
}

// 获取当前配置
function getCurrentConfig() {
    return {
        API: {
            url: document.getElementById('api-url').value,
            cookie: document.getElementById('cookie').value
        },
        SEARCH: {
            base_path: document.getElementById('search-path').value,
            target_suffix: document.getElementById('target-suffix').value,
            enable_target_suffix: document.getElementById('enable-target-suffix').checked.toString(),
            ignore_filename_chars: document.getElementById('ignore-filename-chars').checked.toString(),
            ignore_prefix_count: document.getElementById('ignore-prefix-count').value,
            ignore_suffix_count: document.getElementById('ignore-suffix-count').value
        },
        SHARED: {
            folder: document.getElementById('shared-folder').value
        },
        OPTIONS: {
            strict_search: document.getElementById('strict-search').checked.toString()
        }
    };
}

// 显示进度模态框
function showProgressModal() {
    const modal = document.getElementById('progress-modal');
    modal.style.display = 'flex';

    // 重置进度
    document.getElementById('progress-fill').style.width = '0%';
    document.getElementById('progress-text').textContent = '正在处理...';
    document.getElementById('progress-results').innerHTML = '';
    document.getElementById('close-progress-btn').style.display = 'none';
}

// 关闭进度模态框
function closeProgressModal() {
    const modal = document.getElementById('progress-modal');
    modal.style.display = 'none';

    if (taskCheckInterval) {
        clearInterval(taskCheckInterval);
        taskCheckInterval = null;
    }

    currentTaskId = null;
}

// 开始任务监控
function startTaskMonitoring() {
    if (!currentTaskId) return;

    taskCheckInterval = setInterval(async () => {
        try {
            const response = await fetch(`/api/task_status/${currentTaskId}`);
            const status = await response.json();

            if (status.status === 'not_found') {
                logMessage('任务不存在', 'error');
                closeProgressModal();
                return;
            }

            // 更新进度
            const progress = (status.progress / status.total) * 100;
            document.getElementById('progress-fill').style.width = progress + '%';
            document.getElementById('progress-text').textContent = status.message;

            // 更新结果
            if (status.results && status.results.length > 0) {
                const resultsDiv = document.getElementById('progress-results');
                resultsDiv.innerHTML = status.results.join('\n');
                resultsDiv.scrollTop = resultsDiv.scrollHeight;
            }

            // 检查任务是否完成
            if (status.status === 'completed' || status.status === 'error') {
                clearInterval(taskCheckInterval);
                taskCheckInterval = null;

                document.getElementById('close-progress-btn').style.display = 'inline-block';

                if (status.status === 'completed') {
                    logMessage('任务完成: ' + status.message, 'success');
                } else {
                    logMessage('任务失败: ' + status.message, 'error');
                }
            }
        } catch (error) {
            logMessage('获取任务状态失败: ' + error.message, 'error');
            closeProgressModal();
        }
    }, 1000);
}

// 日志输出
function logMessage(message, type = 'info') {
    const console = document.getElementById('console');
    const timestamp = new Date().toLocaleTimeString();
    const logClass = `log-${type}`;

    const logLine = `[${timestamp}] ${message}\n`;

    // 创建带样式的日志元素
    const logElement = document.createElement('span');
    logElement.className = logClass;
    logElement.textContent = logLine;

    console.appendChild(logElement);
    console.scrollTop = console.scrollHeight;
}

// 清空控制台
function clearConsole() {
    document.getElementById('console').innerHTML = '';
    logMessage('控制台已清空', 'info');
}