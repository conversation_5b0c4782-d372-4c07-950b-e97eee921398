<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片对比 - 半托找图WEB版</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/样式.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/对比页面.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <h1><i class="fas fa-balance-scale"></i> 图片对比选择</h1>
            <p class="subtitle">选择要下载的图片</p>
        </header>

        <!-- SKU信息区域 -->
        <section class="sku-info-section">
            <div class="sku-info">
                <h2 id="sku-title">SKU: <span id="current-sku">-</span></h2>
                <p id="product-name">商品名称: <span id="current-product-name">-</span></p>
                <div class="progress-info">
                    <span id="progress-text">进度: 0/0</span>
                </div>
            </div>
        </section>

        <!-- 图片对比区域 -->
        <section class="compare-section">
            <div class="compare-container">
                <!-- API商品图 -->
                <div class="image-panel api-panel">
                    <div class="panel-header">
                        <h3><i class="fas fa-cloud"></i> API商品图</h3>
                        <div class="image-info">
                            <span class="image-source">来源: 商品API</span>
                        </div>
                    </div>
                    <div class="image-container">
                        <div id="api-image-loading" class="loading-placeholder">
                            <i class="fas fa-spinner fa-spin"></i>
                            <p>加载中...</p>
                        </div>
                        <img id="api-image" class="compare-image" style="display: none;" alt="API商品图">
                        <div id="api-image-error" class="error-placeholder" style="display: none;">
                            <i class="fas fa-exclamation-triangle"></i>
                            <p>无法加载API图片</p>
                        </div>
                    </div>
                    <div class="panel-actions">
                        <button id="select-api-btn" class="btn btn-primary" disabled>
                            <i class="fas fa-check"></i> 选择此图片
                        </button>
                    </div>
                </div>

                <!-- 本地图片 -->
                <div class="image-panel local-panel">
                    <div class="panel-header">
                        <h3><i class="fas fa-hdd"></i> 本地图片</h3>
                        <div class="image-info">
                            <span id="local-image-count">找到: 0 张</span>
                        </div>
                    </div>
                    <div class="image-container">
                        <div id="local-images-loading" class="loading-placeholder">
                            <i class="fas fa-spinner fa-spin"></i>
                            <p>搜索中...</p>
                        </div>
                        <div id="local-images-container" class="local-images-grid" style="display: none;">
                            <!-- 本地图片将在这里动态加载 -->
                        </div>
                        <div id="local-images-empty" class="error-placeholder" style="display: none;">
                            <i class="fas fa-folder-open"></i>
                            <p>未找到本地图片</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 操作按钮区域 -->
        <section class="actions-section">
            <div class="action-buttons">
                <button id="skip-btn" class="btn btn-secondary">
                    <i class="fas fa-forward"></i> 跳过此SKU
                </button>
                <button id="back-btn" class="btn btn-info">
                    <i class="fas fa-arrow-left"></i> 返回主页
                </button>
            </div>
        </section>

        <!-- 操作日志 -->
        <section class="console-section">
            <h2><i class="fas fa-terminal"></i> 操作日志</h2>
            <div id="console" class="console"></div>
            <div class="console-controls">
                <button type="button" id="clear-console-btn" class="btn btn-secondary">
                    <i class="fas fa-trash"></i> 清空日志
                </button>
            </div>
        </section>
    </div>

    <!-- 确认对话框 -->
    <div id="confirm-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <h3><i class="fas fa-question-circle"></i> 确认选择</h3>
            <p id="confirm-message">确定要选择这张图片吗？</p>
            <div class="modal-actions">
                <button id="confirm-yes-btn" class="btn btn-success">
                    <i class="fas fa-check"></i> 确定
                </button>
                <button id="confirm-no-btn" class="btn btn-secondary">
                    <i class="fas fa-times"></i> 取消
                </button>
            </div>
        </div>
    </div>

    <!-- 完成对话框 -->
    <div id="complete-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <h3><i class="fas fa-check-circle"></i> 处理完成</h3>
            <p id="complete-message">所有SKU处理完成！</p>
            <div id="complete-summary" class="complete-summary">
                <!-- 处理结果摘要 -->
            </div>
            <div class="modal-actions">
                <button id="complete-ok-btn" class="btn btn-primary">
                    <i class="fas fa-home"></i> 返回主页
                </button>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/图片对比.js') }}"></script>
</body>
</html>