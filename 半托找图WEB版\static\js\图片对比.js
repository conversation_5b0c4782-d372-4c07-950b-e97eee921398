// 图片对比页面脚本

// 全局变量
let skuList = [];
let currentIndex = 0;
let currentImageData = null;
let selectedLocalImage = null;
let downloadResults = [];

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    loadSkuData();
});

// 初始化事件监听器
function initializeEventListeners() {
    // 选择按钮
    document.getElementById('select-api-btn').addEventListener('click', () => selectImage('api'));

    // 操作按钮
    document.getElementById('skip-btn').addEventListener('click', skipCurrentSku);
    document.getElementById('back-btn').addEventListener('click', () => window.location.href = '/');

    // 控制台清空按钮
    document.getElementById('clear-console-btn').addEventListener('click', clearConsole);

    // 确认对话框
    document.getElementById('confirm-yes-btn').addEventListener('click', confirmSelection);
    document.getElementById('confirm-no-btn').addEventListener('click', closeConfirmModal);

    // 完成对话框
    document.getElementById('complete-ok-btn').addEventListener('click', () => window.location.href = '/');
}

// 从URL参数或localStorage加载SKU数据
function loadSkuData() {
    try {
        // 尝试从URL参数获取
        const urlParams = new URLSearchParams(window.location.search);
        const skuParam = urlParams.get('skus');

        if (skuParam) {
            skuList = skuParam.split(',').map(sku => sku.trim()).filter(sku => sku);
        } else {
            // 从localStorage获取
            const storedSkus = localStorage.getItem('compareSkus');
            if (storedSkus) {
                skuList = JSON.parse(storedSkus);
            }
        }

        if (skuList.length === 0) {
            logMessage('没有找到要对比的SKU数据', 'error');
            showCompleteModal('没有SKU数据', '请返回主页重新选择SKU');
            return;
        }

        logMessage(`加载了 ${skuList.length} 个SKU进行对比`, 'success');
        processCurrentSku();

    } catch (error) {
        logMessage('加载SKU数据失败: ' + error.message, 'error');
        showCompleteModal('加载失败', '无法加载SKU数据，请返回主页重试');
    }
}

// 处理当前SKU
async function processCurrentSku() {
    if (currentIndex >= skuList.length) {
        showCompleteModal('处理完成', '所有SKU已处理完成');
        return;
    }

    const sku = skuList[currentIndex];
    updateProgress();

    logMessage(`开始处理SKU: ${sku}`, 'info');

    // 更新界面显示
    document.getElementById('current-sku').textContent = sku;
    document.getElementById('current-product-name').textContent = '搜索中...';

    // 重置界面状态
    resetImagePanels();

    try {
        // 首先搜索SKU获取商品信息
        const skuData = await searchSkuInfo(sku);

        if (skuData.success) {
            document.getElementById('current-product-name').textContent = skuData.product_name;
            currentImageData = skuData;

            // 并行加载API图片和搜索本地图片
            await Promise.all([
                loadApiImage(skuData.thumb_url),
                searchLocalImages(sku, skuData.product_name, skuData.thumb_url)
            ]);
        } else {
            logMessage(`SKU ${sku} 搜索失败: ${skuData.message}`, 'error');
            document.getElementById('current-product-name').textContent = '搜索失败';
            showApiImageError();
            showLocalImagesEmpty();
        }

    } catch (error) {
        logMessage(`处理SKU ${sku} 时出错: ${error.message}`, 'error');
        showApiImageError();
        showLocalImagesEmpty();
    }
}

// 搜索SKU信息
async function searchSkuInfo(sku) {
    const config = await getConfig();

    const response = await fetch('/api/search_sku', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            sku: sku,
            cookie: config.API.cookie
        })
    });

    return await response.json();
}

// 获取配置信息
async function getConfig() {
    const response = await fetch('/api/config');
    return await response.json();
}

// 加载API图片
async function loadApiImage(thumbUrl) {
    const apiImage = document.getElementById('api-image');
    const loadingDiv = document.getElementById('api-image-loading');
    const errorDiv = document.getElementById('api-image-error');
    const selectBtn = document.getElementById('select-api-btn');

    if (!thumbUrl) {
        showApiImageError();
        return;
    }

    // 显示加载状态
    loadingDiv.style.display = 'block';
    apiImage.style.display = 'none';
    errorDiv.style.display = 'none';
    selectBtn.disabled = true;

    try {
        // 创建新的图片对象来测试加载
        const testImage = new Image();

        testImage.onload = function() {
            apiImage.src = thumbUrl;
            apiImage.style.display = 'block';
            loadingDiv.style.display = 'none';
            selectBtn.disabled = false;
            logMessage('API图片加载成功', 'success');
        };

        testImage.onerror = function() {
            showApiImageError();
        };

        testImage.src = thumbUrl;

    } catch (error) {
        showApiImageError();
    }
}

// 显示API图片错误
function showApiImageError() {
    document.getElementById('api-image-loading').style.display = 'none';
    document.getElementById('api-image').style.display = 'none';
    document.getElementById('api-image-error').style.display = 'block';
    document.getElementById('select-api-btn').disabled = true;
    logMessage('API图片加载失败', 'warning');
}

// 搜索本地图片
async function searchLocalImages(sku, productName, thumbUrl) {
    const loadingDiv = document.getElementById('local-images-loading');
    const containerDiv = document.getElementById('local-images-container');
    const emptyDiv = document.getElementById('local-images-empty');
    const countSpan = document.getElementById('local-image-count');

    // 显示加载状态
    loadingDiv.style.display = 'block';
    containerDiv.style.display = 'none';
    emptyDiv.style.display = 'none';

    try {
        const config = await getConfig();

        const response = await fetch('/api/search_images', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                sku: sku,
                product_name: productName,
                thumb_url: thumbUrl,
                config: config
            })
        });

        const result = await response.json();

        if (result.success && result.local_images.length > 0) {
            displayLocalImages(result.local_images);
            countSpan.textContent = `找到: ${result.local_images.length} 张`;
            logMessage(`找到 ${result.local_images.length} 张本地图片`, 'success');
        } else {
            showLocalImagesEmpty();
            countSpan.textContent = '找到: 0 张';
            logMessage('未找到匹配的本地图片', 'warning');
        }

    } catch (error) {
        showLocalImagesEmpty();
        logMessage('搜索本地图片失败: ' + error.message, 'error');
    }
}

// 显示本地图片
function displayLocalImages(images) {
    const containerDiv = document.getElementById('local-images-container');
    const loadingDiv = document.getElementById('local-images-loading');

    // 清空容器
    containerDiv.innerHTML = '';

    images.forEach((image, index) => {
        const imageItem = createLocalImageItem(image, index);
        containerDiv.appendChild(imageItem);
    });

    // 显示容器
    loadingDiv.style.display = 'none';
    containerDiv.style.display = 'grid';
}

// 创建本地图片项
function createLocalImageItem(image, index) {
    const item = document.createElement('div');
    item.className = 'local-image-item';
    item.dataset.index = index;

    item.innerHTML = `
        <img src="${image.url}" alt="${image.name}" loading="lazy">
        <div class="local-image-info">
            <div class="local-image-name">${image.name}</div>
            <div class="local-image-size">${formatFileSize(image.size)}</div>
        </div>
        <div class="selection-indicator">
            <i class="fas fa-check"></i>
        </div>
    `;

    // 添加点击事件
    item.addEventListener('click', () => selectLocalImage(item, image));

    return item;
}

// 显示本地图片为空
function showLocalImagesEmpty() {
    document.getElementById('local-images-loading').style.display = 'none';
    document.getElementById('local-images-container').style.display = 'none';
    document.getElementById('local-images-empty').style.display = 'block';
}

// 选择本地图片
function selectLocalImage(item, image) {
    // 清除之前的选择
    document.querySelectorAll('.local-image-item.selected').forEach(el => {
        el.classList.remove('selected');
    });

    // 选择当前图片
    item.classList.add('selected');
    selectedLocalImage = image;

    logMessage(`选择了本地图片: ${image.name}`, 'info');
}

// 选择图片（API或本地）
function selectImage(type) {
    if (type === 'api') {
        if (!currentImageData || !currentImageData.thumb_url) {
            logMessage('API图片不可用', 'warning');
            return;
        }
        showConfirmModal('API图片', `确定要下载API商品图吗？\nSKU: ${currentImageData.sku}`);
    } else if (type === 'local' && selectedLocalImage) {
        showConfirmModal('本地图片', `确定要下载本地图片吗？\n文件: ${selectedLocalImage.name}`);
    }
}

// 显示确认对话框
function showConfirmModal(imageType, message) {
    document.getElementById('confirm-message').textContent = message;
    document.getElementById('confirm-modal').style.display = 'flex';

    // 存储选择类型
    document.getElementById('confirm-modal').dataset.imageType = imageType === 'API图片' ? 'api' : 'local';
}

// 关闭确认对话框
function closeConfirmModal() {
    document.getElementById('confirm-modal').style.display = 'none';
}

// 确认选择
async function confirmSelection() {
    const modal = document.getElementById('confirm-modal');
    const imageType = modal.dataset.imageType;

    closeConfirmModal();

    try {
        let imageUrl;
        if (imageType === 'api') {
            imageUrl = currentImageData.thumb_url;
        } else {
            imageUrl = selectedLocalImage.url;
        }

        logMessage(`开始下载${imageType === 'api' ? 'API' : '本地'}图片...`, 'info');

        const response = await fetch('/api/download_selected_image', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                sku: skuList[currentIndex],
                image_type: imageType,
                image_url: imageUrl
            })
        });

        const result = await response.json();

        if (result.success) {
            logMessage(`图片下载成功: ${result.file_path}`, 'success');
            downloadResults.push({
                sku: skuList[currentIndex],
                type: imageType,
                success: true,
                file_path: result.file_path
            });
        } else {
            logMessage(`图片下载失败: ${result.message}`, 'error');
            downloadResults.push({
                sku: skuList[currentIndex],
                type: imageType,
                success: false,
                error: result.message
            });
        }

        // 处理下一个SKU
        nextSku();

    } catch (error) {
        logMessage(`下载图片时出错: ${error.message}`, 'error');
        downloadResults.push({
            sku: skuList[currentIndex],
            type: imageType,
            success: false,
            error: error.message
        });
        nextSku();
    }
}

// 跳过当前SKU
function skipCurrentSku() {
    logMessage(`跳过SKU: ${skuList[currentIndex]}`, 'warning');
    downloadResults.push({
        sku: skuList[currentIndex],
        type: 'skipped',
        success: false,
        error: '用户跳过'
    });
    nextSku();
}

// 处理下一个SKU
function nextSku() {
    currentIndex++;
    selectedLocalImage = null;
    currentImageData = null;

    // 延迟一秒后处理下一个，给用户时间看到结果
    setTimeout(() => {
        processCurrentSku();
    }, 1000);
}

// 重置图片面板
function resetImagePanels() {
    // 重置API图片面板
    document.getElementById('api-image-loading').style.display = 'block';
    document.getElementById('api-image').style.display = 'none';
    document.getElementById('api-image-error').style.display = 'none';
    document.getElementById('select-api-btn').disabled = true;

    // 重置本地图片面板
    document.getElementById('local-images-loading').style.display = 'block';
    document.getElementById('local-images-container').style.display = 'none';
    document.getElementById('local-images-empty').style.display = 'none';
    document.getElementById('local-image-count').textContent = '搜索中...';

    // 清除选择状态
    selectedLocalImage = null;
}

// 更新进度显示
function updateProgress() {
    const progressText = document.getElementById('progress-text');
    progressText.textContent = `进度: ${currentIndex + 1}/${skuList.length}`;
}

// 显示完成对话框
function showCompleteModal(title, message) {
    document.getElementById('complete-message').textContent = message;

    // 生成处理结果摘要
    const summary = generateSummary();
    document.getElementById('complete-summary').innerHTML = summary;

    document.getElementById('complete-modal').style.display = 'flex';
}

// 生成处理结果摘要
function generateSummary() {
    if (downloadResults.length === 0) {
        return '<p>没有处理任何SKU</p>';
    }

    const successful = downloadResults.filter(r => r.success).length;
    const failed = downloadResults.filter(r => !r.success).length;
    const skipped = downloadResults.filter(r => r.type === 'skipped').length;

    let summary = `
        <div style="margin-bottom: 15px;">
            <strong>处理结果统计：</strong><br>
            总计: ${downloadResults.length} 个SKU<br>
            成功: ${successful} 个<br>
            失败: ${failed} 个<br>
            跳过: ${skipped} 个
        </div>
        <div style="max-height: 150px; overflow-y: auto;">
            <strong>详细结果：</strong><br>
    `;

    downloadResults.forEach(result => {
        const status = result.success ? '✅' : (result.type === 'skipped' ? '⏭️' : '❌');
        const info = result.success ? result.file_path : result.error;
        summary += `${status} ${result.sku}: ${info}<br>`;
    });

    summary += '</div>';
    return summary;
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 日志输出
function logMessage(message, type = 'info') {
    const console = document.getElementById('console');
    const timestamp = new Date().toLocaleTimeString();
    const logClass = `log-${type}`;

    const logLine = `[${timestamp}] ${message}\n`;

    // 创建带样式的日志元素
    const logElement = document.createElement('span');
    logElement.className = logClass;
    logElement.textContent = logLine;

    console.appendChild(logElement);
    console.scrollTop = console.scrollHeight;
}

// 清空控制台
function clearConsole() {
    document.getElementById('console').innerHTML = '';
    logMessage('控制台已清空', 'info');
}